"""
Module contenant les formules et fonctions pour calculer les proportions et effectuer des prédictions.
"""
from typing import Dict, List, Tuple

# Proportions du point 0 (référence)
POINT_ZERO_PROPORTIONS = {
    # Index1
    'INDEX1': {
        '0': 49.6719,
        '1': 50.3281
    },
    # Index2
    'INDEX2': {
        'A': 37.8823,
        'B': 31.7712,
        'C': 30.3465
    },
    # Index3
    'INDEX3': {
        'BANKER': 45.8512,
        'PLAYER': 44.6365,
        'TIE': 9.5124
    },
    # Index5
    'INDEX5': {
        '0_A_BANKER': 8.5136,
        '0_B_BANKER': 7.6907,
        '0_C_BANKER': 7.7903,
        '1_A_BANKER': 8.6389,
        '1_B_BANKER': 6.5479,
        '1_C_BANKER': 7.8929,
        '0_A_PLAYER': 8.5240,
        '0_B_PLAYER': 7.6907,
        '0_C_PLAYER': 5.9617,
        '1_A_PLAYER': 8.6361,
        '1_B_PLAYER': 7.7888,
        '1_C_PLAYER': 6.0352,
        '0_A_TIE': 1.7719,
        '0_B_TIE': 1.6281,
        '0_C_TIE': 1.3241,
        '1_A_TIE': 1.7978,
        '1_B_TIE': 1.6482,
        '1_C_TIE': 1.3423
    }
}

# Nombre total de mains de référence
TOTAL_MAINS_REFERENCE = 60  # Modifié pour représenter une seule partie de 60 mains

def calculer_prochain_index1(index1_actuel: int, index2_actuel: str) -> int:
    """
    Détermine la valeur de INDEX1 pour la prochaine main en fonction de INDEX1 et INDEX2 actuels.
    
    Args:
        index1_actuel (int): Valeur actuelle de INDEX1 (0 ou 1)
        index2_actuel (str): Valeur actuelle de INDEX2 ('A', 'B', ou 'C')
    
    Returns:
        int: Valeur prédite de INDEX1 pour la prochaine main
    """
    if index2_actuel == 'C':
        return 1 - index1_actuel  # Inverse pour C (0->1, 1->0)
    else:
        return index1_actuel  # Reste le même pour A et B

def calculer_proportions_actuelles(historique_mains: List[Dict], total_mains: int = TOTAL_MAINS_REFERENCE) -> Dict[str, Dict[str, float]]:
    """
    Calcule les proportions actuelles après avoir pris en compte l'historique des mains.
    
    Args:
        historique_mains (List[Dict]): Liste des mains jouées jusqu'à présent
        total_mains (int): Nombre total de mains de référence
    
    Returns:
        Dict[str, Dict[str, float]]: Dictionnaire des proportions actuelles
    """
    # Copie des proportions initiales
    proportions = {
        'INDEX1': POINT_ZERO_PROPORTIONS['INDEX1'].copy(),
        'INDEX2': POINT_ZERO_PROPORTIONS['INDEX2'].copy(),
        'INDEX3': POINT_ZERO_PROPORTIONS['INDEX3'].copy(),
        'INDEX5': POINT_ZERO_PROPORTIONS['INDEX5'].copy()
    }
    
    # Nombre de mains à ajouter à notre distribution
    nb_mains_ajoutees = len([m for m in historique_mains if m.get('main_number') is not None])
    
    # Si aucune main n'a été jouée, retournons les proportions du point 0
    if nb_mains_ajoutees == 0:
        return proportions
        
    # Compter les occurrences dans l'historique des mains
    compteurs = {
        'INDEX1': {'0': 0, '1': 0},
        'INDEX2': {'A': 0, 'B': 0, 'C': 0},
        'INDEX3': {'BANKER': 0, 'PLAYER': 0, 'TIE': 0},
        'INDEX5': {k: 0 for k in POINT_ZERO_PROPORTIONS['INDEX5'].keys()}
    }
    
    for main in historique_mains:
        if main.get('main_number') is None:
            continue
            
        index1 = main.get('index1')
        index2 = main.get('index2')
        index3 = main.get('index3')
        index5 = main.get('index5')
        
        if index1 is not None and index1 != "":
            compteurs['INDEX1'][str(index1)] += 1
        
        if index2:
            compteurs['INDEX2'][index2] += 1
            
        if index3:
            compteurs['INDEX3'][index3] += 1
            
        if index5:
            compteurs['INDEX5'][index5] += 1
    
    # Calculer les nouvelles proportions
    for category in ['INDEX1', 'INDEX2', 'INDEX3', 'INDEX5']:
        for key in proportions[category]:
            # Calculer les nouvelles proportions en intégrant les mains observées
            valeur_point_zero = POINT_ZERO_PROPORTIONS[category][key] / 100 * TOTAL_MAINS_REFERENCE
            nouvelle_valeur = (valeur_point_zero + compteurs[category][key]) / (TOTAL_MAINS_REFERENCE + nb_mains_ajoutees) * 100
            proportions[category][key] = nouvelle_valeur
            
    return proportions

def predire_prochain_resultat(
    main_actuelle: Dict, 
    historique_mains: List[Dict], 
    proportions_actuelles: Dict[str, Dict[str, float]]
) -> str:
    """
    Prédit le prochain résultat (BANKER, PLAYER ou TIE) en fonction de l'état actuel et des proportions.

    Conditions de prédiction basées sur INDEX1_INDEX2 de la main actuelle :
    🔴 BANKER favorisé : 1_B, 0_C, 0_B
    🔵 PLAYER favorisé : 0_A, 1_A, 1_C

    Args:
        main_actuelle (Dict): La main actuelle
        historique_mains (List[Dict]): Liste des mains jouées jusqu'à présent
        proportions_actuelles (Dict[str, Dict[str, float]]): Proportions actuelles calculées

    Returns:
        str: Résultat prédit ('BANKER', 'PLAYER') ou None si conditions non réunies
    """
    # Détermine le prochain INDEX1 en fonction des règles
    index1_actuel = main_actuelle.get('index1')
    index2_actuel = main_actuelle.get('index2')

    # Définir les conditions de prédiction basées sur INDEX1_INDEX2
    BANKER_FAVORISE = ['1_B', '0_C', '0_B']  # 🔴 TOP 3 - BANKER FAVORISÉ
    PLAYER_FAVORISE = ['0_A', '1_A', '1_C']  # 🔵 TOP 3 - PLAYER FAVORISÉ

    if index1_actuel is None or index2_actuel is None or index1_actuel == "" or index2_actuel == "":
        # Si c'est la toute première main, on se base uniquement sur les proportions du point 0
        # Comparer seulement BANKER et PLAYER (pas TIE)
        banker_prop = proportions_actuelles['INDEX3']['BANKER']
        player_prop = proportions_actuelles['INDEX3']['PLAYER']
        
        # Déterminer lequel est le plus éloigné de sa proportion cible (en-dessous)
        banker_diff = POINT_ZERO_PROPORTIONS['INDEX3']['BANKER'] - banker_prop
        player_diff = POINT_ZERO_PROPORTIONS['INDEX3']['PLAYER'] - player_prop
        
        # Retourner le résultat ayant la plus grande différence positive (le plus en déficit)
        # Si tous sont au-dessus de leurs proportions cibles, prendre le moins excédentaire
        if banker_diff > player_diff:
            return 'BANKER'
        else:
            return 'PLAYER'
    
    # Convertir index1_actuel en entier s'il ne l'est pas déjà
    if not isinstance(index1_actuel, int):
        try:
            index1_actuel = int(index1_actuel)
        except (ValueError, TypeError):
            print(f"Erreur: index1_actuel n'est pas un entier valide: {index1_actuel}")
            # Fallback sur une valeur par défaut
            if proportions_actuelles['INDEX3']['BANKER'] > proportions_actuelles['INDEX3']['PLAYER']:
                return 'BANKER'
            else:
                return 'PLAYER'
    
    # Calculer le prochain INDEX1
    prochain_index1 = calculer_prochain_index1(index1_actuel, index2_actuel)
    
    # Calculer les écarts pour BANKER et PLAYER dans chaque valeur d'INDEX2 possible
    ecarts = {'BANKER': {}, 'PLAYER': {}}
    
    # Pour chaque type d'INDEX2 possible (A, B, C)
    for index2 in ['A', 'B', 'C']:
        # Calculer l'écart pour BANKER et PLAYER (pas TIE)
        for resultat in ['BANKER', 'PLAYER']:
            # Assurons-nous que prochain_index1 est un entier et convertissons-le en str
            index5 = f"{prochain_index1}_{index2}_{resultat}"
            try:
                proportion_cible = POINT_ZERO_PROPORTIONS['INDEX5'][index5]
                proportion_actuelle = proportions_actuelles['INDEX5'][index5]
            except KeyError:
                print(f"Erreur de clé: {index5} n'existe pas dans les proportions")
                # Utiliser une approche alternative si la clé n'existe pas
                continue
            
            # L'écart est la différence entre la proportion cible et la proportion actuelle
            # Un écart positif signifie qu'on est en déficit par rapport à la cible
            ecarts[resultat][index2] = proportion_cible - proportion_actuelle
    
    # Maintenant, nous devons estimer quelle sera la valeur INDEX2 la plus probable
    # pour la prochaine main. Nous utilisons les proportions actuelles comme approximation
    index2_probabilities = proportions_actuelles['INDEX2']
    
    # Vérifier si nous avons assez de données pour faire une prédiction
    resultats_manquants = []
    for resultat in ['BANKER', 'PLAYER']:
        if not all(idx2 in ecarts[resultat] for idx2 in ['A', 'B', 'C']):
            resultats_manquants.append(resultat)
    
    if resultats_manquants:
        # Si des données sont manquantes, utiliser directement les proportions INDEX3
        banker_diff = POINT_ZERO_PROPORTIONS['INDEX3']['BANKER'] - proportions_actuelles['INDEX3']['BANKER']
        player_diff = POINT_ZERO_PROPORTIONS['INDEX3']['PLAYER'] - proportions_actuelles['INDEX3']['PLAYER']
        
        if banker_diff > player_diff:
            return 'BANKER'
        else:
            return 'PLAYER'
    
    # Calculer le score global pondéré pour BANKER et PLAYER seulement
    score_banker = sum(ecarts['BANKER'].get(idx2, 0) * (index2_probabilities[idx2] / 100) for idx2 in ['A', 'B', 'C'] if idx2 in ecarts['BANKER'])
    score_player = sum(ecarts['PLAYER'].get(idx2, 0) * (index2_probabilities[idx2] / 100) for idx2 in ['A', 'B', 'C'] if idx2 in ecarts['PLAYER'])

    # Déterminer la prédiction basée sur les scores
    prediction_brute = 'BANKER' if score_banker > score_player else 'PLAYER'

    # Appliquer les conditions de filtrage basées sur INDEX1_INDEX2 de la main actuelle
    index1_index2_actuel = f"{index1_actuel}_{index2_actuel}"

    # Vérifier si les conditions sont réunies pour émettre la prédiction
    if prediction_brute == 'BANKER':
        # BANKER ne peut être prédit que si INDEX1_INDEX2 est dans {1_B, 0_C, 0_B}
        if index1_index2_actuel in BANKER_FAVORISE:
            return 'BANKER'
        else:
            return None  # Conditions non réunies pour prédire BANKER
    else:  # prediction_brute == 'PLAYER'
        # PLAYER ne peut être prédit que si INDEX1_INDEX2 est dans {0_A, 1_A, 1_C}
        if index1_index2_actuel in PLAYER_FAVORISE:
            return 'PLAYER'
        else:
            return None  # Conditions non réunies pour prédire PLAYER
