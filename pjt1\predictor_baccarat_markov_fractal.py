"""
Module principal pour effectuer des prédictions sur les jeux de baccarat en utilisant
un modèle qui combine des concepts Markov et fractals pour retourner aux proportions idéales.
"""
from typing import Dict, List, Any, Tuple
import os
import json
from datetime import datetime
import analyseur

class PredicteurBaccaratMarkovFractal:
    def __init__(self, fichier_json: str):
        """
        Initialise le prédicteur avec le fichier de données JSON.
        
        Args:
            fichier_json (str): Chemin vers le fichier JSON contenant les données.
        """
        self.fichier_json = fichier_json
        self.analyseur = analyseur.Analyseur(fichier_json)
        
    def executer_predictions(self) -> Dict[str, Any]:
        """
        Exécute des prédictions sur toutes les parties du jeu de données.
        
        Returns:
            Dict[str, Any]: Résultats des prédictions avec statistiques.
        """
        print(f"Démarrage des prédictions sur le fichier: {self.fichier_json}")
        
        # Analyser toutes les parties
        resultats_parties = self.analyseur.analyser_toutes_parties()
        
        # Calculer la précision globale
        precision_globale = self.analyseur.calculer_precision_globale(resultats_parties)
        
        # Calculer les statistiques par partie
        stats_parties = {}
        for partie_num, resultats in resultats_parties.items():
            total_predictions = 0
            predictions_correctes = 0
            
            for resultat in resultats:
                if resultat['prediction_correcte'] is not None:
                    total_predictions += 1
                    if resultat['prediction_correcte']:
                        predictions_correctes += 1
            
            precision_partie = 0
            if total_predictions > 0:
                precision_partie = (predictions_correctes / total_predictions) * 100
                
            stats_parties[partie_num] = {
                'total_predictions': total_predictions,
                'predictions_correctes': predictions_correctes,
                'precision_pourcentage': precision_partie
            }
        
        # Agréger les résultats
        resultats_globaux = {
            'date_execution': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'fichier_source': self.fichier_json,
            'precision_globale': precision_globale,
            'statistiques_parties': stats_parties,
            'details_predictions': resultats_parties
        }
        
        return resultats_globaux
    
    def sauvegarder_resultats(self, resultats: Dict[str, Any], fichier_sortie: str = None) -> str:
        """
        Sauvegarde les résultats des prédictions dans un fichier texte détaillé.
        
        Args:
            resultats (Dict[str, Any]): Résultats des prédictions à sauvegarder.
            fichier_sortie (str, optional): Chemin vers le fichier de sortie. Si None, un nom de fichier
                                           sera généré automatiquement.
        
        Returns:
            str: Chemin vers le fichier où les résultats ont été sauvegardés.
        """
        if fichier_sortie is None:
            # Générer un nom de fichier basé sur la date et l'heure actuelles
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            nom_fichier_base = os.path.basename(self.fichier_json).replace('.json', '')
            fichier_sortie = f"rapport_predictions_{nom_fichier_base}_{timestamp}.txt"
        
        # Sauvegarder les résultats dans un fichier texte
        with open(fichier_sortie, 'w', encoding='utf-8') as f:
            # En-tête du rapport
            f.write("="*80 + "\n")
            f.write(f"RAPPORT DÉTAILLÉ DES PRÉDICTIONS BACCARAT\n")
            f.write("="*80 + "\n\n")
            
            # Informations générales
            f.write(f"Date d'exécution: {resultats['date_execution']}\n")
            f.write(f"Fichier source: {resultats['fichier_source']}\n")
            f.write("-"*80 + "\n\n")
            
            # Précision globale
            precision_globale = resultats['precision_globale']
            f.write("PRÉCISION GLOBALE:\n")
            f.write(f"Total prédictions: {precision_globale['total_predictions']}\n")
            f.write(f"Prédictions correctes: {precision_globale['predictions_correctes']}\n")
            f.write(f"Taux de précision: {precision_globale['precision_pourcentage']:.4f}%\n")
            f.write("-"*80 + "\n\n")
            
            # Statistiques par partie
            f.write("STATISTIQUES PAR PARTIE:\n")
            f.write("-"*80 + "\n")
            f.write(f"{'Partie':^10} | {'Total':^15} | {'Correctes':^15} | {'Précision':^15}\n")
            f.write("-"*80 + "\n")
            
            parties_stats = resultats['statistiques_parties']
            parties_triees = sorted(
                [(partie_num, stats) for partie_num, stats in parties_stats.items()],
                key=lambda x: x[0]
            )
            
            for partie_num, stats in parties_triees:
                f.write(f"{partie_num:^10} | {stats['total_predictions']:^15} | {stats['predictions_correctes']:^15} | {stats['precision_pourcentage']:^15.4f}%\n")
            
            f.write("-"*80 + "\n\n")
            
            # Top 5 des parties avec la meilleure précision
            parties_triees_precision = sorted(
                [(partie_num, stats) for partie_num, stats in parties_stats.items() if stats['total_predictions'] > 0],
                key=lambda x: x[1]['precision_pourcentage'],
                reverse=True
            )
            
            f.write("TOP 5 DES PARTIES AVEC LA MEILLEURE PRÉCISION:\n")
            f.write("-"*80 + "\n")
            for i, (partie_num, stats) in enumerate(parties_triees_precision[:5], 1):
                f.write(f"{i}. Partie {partie_num}: {stats['precision_pourcentage']:.4f}% ({stats['predictions_correctes']}/{stats['total_predictions']})\n")
            
            f.write("-"*80 + "\n\n")
            
            # Détails des prédictions par partie
            f.write("DÉTAILS DES PRÉDICTIONS PAR PARTIE:\n")
            f.write("="*80 + "\n\n")
            
            details_predictions = resultats['details_predictions']
            for partie_num in sorted(details_predictions.keys()):
                predictions_partie = details_predictions[partie_num]
                
                f.write(f"PARTIE {partie_num}\n")
                f.write("-"*80 + "\n")
                f.write(f"{'Main':^8} | {'Prédiction':^12} | {'Résultat réel':^15} | {'Correcte':^10}\n")
                f.write("-"*80 + "\n")
                
                for pred in predictions_partie:
                    # Gérer correctement les valeurs None ou vides
                    main_num = pred.get('main_number', '')
                    main_num = 'N/A' if main_num is None else str(main_num)
                    
                    prediction = pred.get('prediction', '')
                    prediction = 'N/A' if prediction is None else str(prediction)
                    
                    resultat_reel = pred.get('resultat_reel', '')
                    resultat_reel = 'N/A' if resultat_reel is None else str(resultat_reel)
                    
                    if pred.get('prediction_correcte') is None:
                        correcte = 'N/A'
                    elif pred.get('prediction_correcte'):
                        correcte = 'OUI'
                    else:
                        correcte = 'NON'
                        
                    f.write(f"{main_num:^8} | {prediction:^12} | {resultat_reel:^15} | {correcte:^10}\n")
                
                # Ajouter des statistiques de synthèse pour cette partie
                stats_partie = parties_stats.get(partie_num, {})
                total = stats_partie.get('total_predictions', 0)
                correctes = stats_partie.get('predictions_correctes', 0)
                precision = stats_partie.get('precision_pourcentage', 0)
                
                f.write("-"*80 + "\n")
                f.write(f"SYNTHÈSE: Total={total}, Correctes={correctes}, Précision={precision:.4f}%\n")
                f.write("\n\n")
            
        print(f"Rapport détaillé sauvegardé dans le fichier: {fichier_sortie}")
        return fichier_sortie
    
    def afficher_resume(self, resultats: Dict[str, Any]) -> None:
        """
        Affiche un résumé des résultats des prédictions.
        
        Args:
            resultats (Dict[str, Any]): Résultats des prédictions à afficher.
        """
        print("\n" + "="*50)
        print("RÉSUMÉ DES PRÉDICTIONS")
        print("="*50)
        
        # Informations générales
        print(f"Date d'exécution: {resultats['date_execution']}")
        print(f"Fichier source: {resultats['fichier_source']}")
        print("-"*50)
        
        # Précision globale
        precision_globale = resultats['precision_globale']
        print("PRÉCISION GLOBALE:")
        print(f"Total prédictions: {precision_globale['total_predictions']}")
        print(f"Prédictions correctes: {precision_globale['predictions_correctes']}")
        print(f"Taux de précision: {precision_globale['precision_pourcentage']:.4f}%")
        print("-"*50)
        
        # Top 5 des parties avec la meilleure précision
        parties_stats = resultats['statistiques_parties']
        parties_triees = sorted(
            [(partie_num, stats) for partie_num, stats in parties_stats.items() if stats['total_predictions'] > 0],
            key=lambda x: x[1]['precision_pourcentage'],
            reverse=True
        )
        
        print("TOP 5 DES PARTIES AVEC LA MEILLEURE PRÉCISION:")
        for i, (partie_num, stats) in enumerate(parties_triees[:5], 1):
            print(f"{i}. Partie {partie_num}: {stats['precision_pourcentage']:.4f}% "
                 f"({stats['predictions_correctes']}/{stats['total_predictions']})")
        
        print("\n" + "="*50)
        print("Analyse terminée!")
        print("="*50)

def main():
    """
    Point d'entrée principal pour exécuter le prédicteur.
    """
    print("="*80)
    print("CONFIGURATION SPÉCIALE: Base de référence de 60 mains au lieu de 58.8M")
    print("Les proportions de départ sont identiques mais l'impact de chaque nouvelle main est amplifié")
    print("="*80)
    
    # Chemin vers le fichier de données
    fichier_json = "dataset_baccarat_lupasco_20250704_092825_condensed.json"
    
    # Créer et exécuter le prédicteur
    predicteur = PredicteurBaccaratMarkovFractal(fichier_json)
    resultats = predicteur.executer_predictions()
    
    # Sauvegarder les résultats dans un fichier texte détaillé
    fichier_rapport = predicteur.sauvegarder_resultats(resultats)
    
    # Afficher un résumé à l'écran
    predicteur.afficher_resume(resultats)
    
    print(f"\nUn rapport détaillé a été généré dans le fichier: {fichier_rapport}")

if __name__ == "__main__":
    main()
