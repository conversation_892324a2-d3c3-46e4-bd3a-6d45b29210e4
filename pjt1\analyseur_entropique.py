"""
🎓 MODULE D'ANALYSE ENTROPIQUE AVANCÉE - EXPERT STATISTICIEN
===============================================================

Analyse statistique approfondie du système de prédiction baccarat :
- Entropie de Shannon, Rényi, conditionnelle
- Information mutuelle et divergence KL  
- Autocorrélation temporelle des INDEX
- Détection d'anomalies et singularités statistiques
- Distribution des entropies par partie

Auteur : Expert Statisticien PhD
Spécialisation : Théorie de l'Information & Analyse Entropique
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.stats import entropy
import matplotlib.pyplot as plt
from collections import Counter, defaultdict
import json
from typing import Dict, List, Tuple, Optional, Any
import warnings
import io_json
import formules
warnings.filterwarnings('ignore')

class AnalyseurEntropique:
    """
    Analyseur statistique expert pour système baccarat
    Implémente les méthodes d'analyse entropique de niveau PhD
    """
    
    def __init__(self, fichier_json: str):
        """
        Initialise l'analyseur avec les données du fichier JSON
        
        Args:
            fichier_json: Chemin vers le fichier de données
        """
        self.fichier_json = fichier_json
        self.donnees = io_json.charger_donnees_json(fichier_json)
        self.parties = io_json.extraire_parties(self.donnees)
        self.resultats_analyse = {}
        self.seuil_anomalie = 2.5  # Z-score pour détection anomalies
        
        print(f"🎓 ANALYSEUR ENTROPIQUE INITIALISÉ")
        print(f"📊 Parties chargées : {len(self.parties)}")

        # Calculer les entropies théoriques du système
        self.entropies_theoriques_systeme = self._calculer_entropies_theoriques_systeme()

    def _calculer_entropies_theoriques_systeme(self) -> Dict[str, Any]:
        """
        Calcule toutes les entropies théoriques du système basées sur POINT_ZERO

        Returns:
            Dict: Entropies théoriques complètes du système
        """
        entropies_theoriques = {}

        # Entropies Shannon théoriques
        entropies_theoriques['shannon'] = {}
        for index_name in ['INDEX1', 'INDEX2', 'INDEX3', 'INDEX5']:
            entropies_theoriques['shannon'][index_name] = self.calculer_entropie_shannon_theorique(index_name)

        # Entropies Rényi théoriques (α=2)
        entropies_theoriques['renyi_alpha2'] = {}
        for index_name in ['INDEX1', 'INDEX2', 'INDEX3', 'INDEX5']:
            entropies_theoriques['renyi_alpha2'][index_name] = self.calculer_entropie_renyi_theorique(index_name, alpha=2.0)

        # Information mutuelle théorique
        entropies_theoriques['information_mutuelle'] = {}
        index_pairs = [('INDEX1', 'INDEX2'), ('INDEX1', 'INDEX3'), ('INDEX2', 'INDEX3')]
        for idx1, idx2 in index_pairs:
            key = f"{idx1}_{idx2}"
            entropies_theoriques['information_mutuelle'][key] = self.calculer_information_mutuelle_theorique(idx1, idx2)

        return entropies_theoriques

    def calculer_entropie_shannon_theorique(self, index_name: str) -> float:
        """
        Calcule l'entropie de Shannon théorique basée sur les proportions POINT_ZERO
        H(X) = -Σ p(x) log₂ p(x)

        Args:
            index_name: Nom de l'INDEX ('INDEX1', 'INDEX2', 'INDEX3', 'INDEX5')

        Returns:
            float: Entropie théorique en bits
        """
        if index_name not in formules.POINT_ZERO_PROPORTIONS:
            return 0.0

        proportions_dict = formules.POINT_ZERO_PROPORTIONS[index_name]

        # Convertir les pourcentages en probabilités
        probabilites = [prop/100.0 for prop in proportions_dict.values()]

        # Entropie de Shannon (base 2)
        return entropy(probabilites, base=2)

    def calculer_entropie_shannon_observee(self, sequence: List) -> float:
        """
        Calcule l'entropie de Shannon observée dans une séquence
        H(X) = -Σ p(x) log₂ p(x)

        Args:
            sequence: Séquence de valeurs observées

        Returns:
            float: Entropie observée en bits
        """
        if not sequence:
            return 0.0

        # Filtrer les valeurs None/vides
        sequence_clean = [x for x in sequence if x is not None and x != ""]
        if not sequence_clean:
            return 0.0

        # Comptage des occurrences
        compteur = Counter(sequence_clean)
        total = len(sequence_clean)

        # Calcul des probabilités observées
        probabilites = [count/total for count in compteur.values()]

        # Entropie de Shannon (base 2)
        return entropy(probabilites, base=2)
    
    def calculer_entropie_renyi_theorique(self, index_name: str, alpha: float = 2.0) -> float:
        """
        Calcule l'entropie de Rényi théorique basée sur les proportions POINT_ZERO
        Hₐ(X) = 1/(1-α) log₂ Σ p(x)ᵅ

        Args:
            index_name: Nom de l'INDEX ('INDEX1', 'INDEX2', 'INDEX3', 'INDEX5')
            alpha: Paramètre de Rényi (α ≠ 1)

        Returns:
            float: Entropie de Rényi théorique en bits
        """
        if alpha == 1.0:
            return self.calculer_entropie_shannon_theorique(index_name)

        if index_name not in formules.POINT_ZERO_PROPORTIONS:
            return 0.0

        proportions_dict = formules.POINT_ZERO_PROPORTIONS[index_name]

        # Convertir les pourcentages en probabilités
        probabilites = [prop/100.0 for prop in proportions_dict.values()]

        # Calcul Rényi
        somme_alpha = sum(p**alpha for p in probabilites)

        if somme_alpha <= 0:
            return 0.0

        return (1/(1-alpha)) * np.log2(somme_alpha)

    def calculer_entropie_renyi_observee(self, sequence: List, alpha: float = 2.0) -> float:
        """
        Calcule l'entropie de Rényi observée dans une séquence
        Hₐ(X) = 1/(1-α) log₂ Σ p(x)ᵅ

        Args:
            sequence: Séquence de valeurs observées
            alpha: Paramètre de Rényi (α ≠ 1)

        Returns:
            float: Entropie de Rényi observée en bits
        """
        if alpha == 1.0:
            return self.calculer_entropie_shannon_observee(sequence)

        if not sequence:
            return 0.0

        sequence_clean = [x for x in sequence if x is not None and x != ""]
        if not sequence_clean:
            return 0.0

        compteur = Counter(sequence_clean)
        total = len(sequence_clean)
        probabilites = [count/total for count in compteur.values()]

        # Calcul Rényi
        somme_alpha = sum(p**alpha for p in probabilites)

        if somme_alpha <= 0:
            return 0.0

        return (1/(1-alpha)) * np.log2(somme_alpha)
    
    def calculer_information_mutuelle_theorique(self, index1_name: str, index2_name: str) -> float:
        """
        Calcule l'information mutuelle théorique entre deux INDEX
        I(X;Y) = H(X) + H(Y) - H(X,Y)

        Args:
            index1_name: Premier INDEX
            index2_name: Deuxième INDEX

        Returns:
            float: Information mutuelle théorique en bits
        """
        # Pour les INDEX simples, calculer directement
        if index1_name in ['INDEX1', 'INDEX2', 'INDEX3'] and index2_name in ['INDEX1', 'INDEX2', 'INDEX3']:
            h_x = self.calculer_entropie_shannon_theorique(index1_name)
            h_y = self.calculer_entropie_shannon_theorique(index2_name)

            # Pour l'entropie jointe, utiliser INDEX5 qui contient les combinaisons
            if (index1_name, index2_name) in [('INDEX1', 'INDEX2'), ('INDEX2', 'INDEX1')]:
                # INDEX5 contient INDEX1_INDEX2_INDEX3, donc on doit marginaliser
                h_jointe = self._calculer_entropie_jointe_index1_index2()
            elif (index1_name, index2_name) in [('INDEX1', 'INDEX3'), ('INDEX3', 'INDEX1')]:
                h_jointe = self._calculer_entropie_jointe_index1_index3()
            elif (index1_name, index2_name) in [('INDEX2', 'INDEX3'), ('INDEX3', 'INDEX2')]:
                h_jointe = self._calculer_entropie_jointe_index2_index3()
            else:
                return 0.0

            return h_x + h_y - h_jointe

        return 0.0

    def _calculer_entropie_jointe_index1_index2(self) -> float:
        """Calcule H(INDEX1, INDEX2) en marginalisant INDEX5"""
        # Marginaliser INDEX5 sur INDEX3 pour obtenir INDEX1_INDEX2
        joint_probs = {}

        for index5_key, prob in formules.POINT_ZERO_PROPORTIONS['INDEX5'].items():
            # index5_key format: "INDEX1_INDEX2_INDEX3"
            parts = index5_key.split('_')
            if len(parts) == 3:
                index1_index2_key = f"{parts[0]}_{parts[1]}"
                if index1_index2_key not in joint_probs:
                    joint_probs[index1_index2_key] = 0.0
                joint_probs[index1_index2_key] += prob / 100.0

        # Calculer entropie
        probabilites = list(joint_probs.values())
        return entropy(probabilites, base=2)

    def _calculer_entropie_jointe_index1_index3(self) -> float:
        """Calcule H(INDEX1, INDEX3) en marginalisant INDEX5"""
        joint_probs = {}

        for index5_key, prob in formules.POINT_ZERO_PROPORTIONS['INDEX5'].items():
            parts = index5_key.split('_')
            if len(parts) == 3:
                index1_index3_key = f"{parts[0]}_{parts[2]}"
                if index1_index3_key not in joint_probs:
                    joint_probs[index1_index3_key] = 0.0
                joint_probs[index1_index3_key] += prob / 100.0

        probabilites = list(joint_probs.values())
        return entropy(probabilites, base=2)

    def _calculer_entropie_jointe_index2_index3(self) -> float:
        """Calcule H(INDEX2, INDEX3) en marginalisant INDEX5"""
        joint_probs = {}

        for index5_key, prob in formules.POINT_ZERO_PROPORTIONS['INDEX5'].items():
            parts = index5_key.split('_')
            if len(parts) == 3:
                index2_index3_key = f"{parts[1]}_{parts[2]}"
                if index2_index3_key not in joint_probs:
                    joint_probs[index2_index3_key] = 0.0
                joint_probs[index2_index3_key] += prob / 100.0

        probabilites = list(joint_probs.values())
        return entropy(probabilites, base=2)

    def calculer_information_mutuelle_observee(self, seq_x: List, seq_y: List) -> float:
        """
        Calcule l'information mutuelle observée : I(X;Y) = H(X) + H(Y) - H(X,Y)

        Args:
            seq_x: Première séquence observée
            seq_y: Deuxième séquence observée

        Returns:
            float: Information mutuelle observée en bits
        """
        if len(seq_x) != len(seq_y):
            return 0.0

        # Nettoyer les séquences
        pairs_clean = [(x, y) for x, y in zip(seq_x, seq_y)
                      if x is not None and y is not None and x != "" and y != ""]

        if not pairs_clean:
            return 0.0

        seq_x_clean = [pair[0] for pair in pairs_clean]
        seq_y_clean = [pair[1] for pair in pairs_clean]

        h_x = self.calculer_entropie_shannon_observee(seq_x_clean)
        h_y = self.calculer_entropie_shannon_observee(seq_y_clean)

        seq_jointe = pairs_clean
        h_jointe = self.calculer_entropie_shannon_observee(seq_jointe)

        return h_x + h_y - h_jointe
    
    def calculer_autocorrelation(self, sequence: List, max_lag: int = 10) -> Dict[int, float]:
        """
        Calcule l'autocorrélation : ρ(τ) = Cov(X_t, X_{t+τ})/Var(X)
        
        Args:
            sequence: Séquence temporelle
            max_lag: Décalage maximum à analyser
            
        Returns:
            Dict[int, float]: Autocorrélations par décalage
        """
        # Convertir en séquence numérique pour l'autocorrélation
        sequence_clean = [x for x in sequence if x is not None and x != ""]
        if len(sequence_clean) < max_lag + 1:
            return {}
            
        # Encoder les valeurs catégorielles
        unique_vals = list(set(sequence_clean))
        val_to_num = {val: i for i, val in enumerate(unique_vals)}
        sequence_num = [val_to_num[val] for val in sequence_clean]
        
        # Calculer autocorrélations
        autocorr = {}
        series = pd.Series(sequence_num)
        
        for lag in range(max_lag + 1):
            if len(series) > lag:
                autocorr[lag] = series.autocorr(lag=lag)
                if pd.isna(autocorr[lag]):
                    autocorr[lag] = 0.0
            else:
                autocorr[lag] = 0.0
                
        return autocorr
    
    def detecter_anomalies_zscore(self, sequence: List, seuil: float = 2.5) -> Dict[str, Any]:
        """
        Détecte les anomalies via z-scores
        
        Args:
            sequence: Séquence à analyser
            seuil: Seuil z-score pour anomalie
            
        Returns:
            Dict: Statistiques d'anomalies
        """
        sequence_clean = [x for x in sequence if x is not None and x != ""]
        if not sequence_clean:
            return {"anomalies": [], "stats": {}}
            
        # Compter les occurrences
        compteur = Counter(sequence_clean)
        counts = list(compteur.values())
        
        if len(counts) < 2:
            return {"anomalies": [], "stats": {"mean": counts[0] if counts else 0, "std": 0}}
            
        # Calculer z-scores
        mean_count = np.mean(counts)
        std_count = np.std(counts)
        
        anomalies = []
        if std_count > 0:
            for val, count in compteur.items():
                z_score = abs(count - mean_count) / std_count
                if z_score > seuil:
                    anomalies.append({
                        "valeur": val,
                        "count": count,
                        "z_score": z_score,
                        "type": "haute" if count > mean_count else "basse"
                    })
        
        return {
            "anomalies": anomalies,
            "stats": {
                "mean": mean_count,
                "std": std_count,
                "total_values": len(sequence_clean),
                "unique_values": len(compteur)
            }
        }
    
    def analyser_partie_entropique(self, partie: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyse entropique complète d'une partie
        
        Args:
            partie: Dictionnaire représentant une partie
            
        Returns:
            Dict: Résultats d'analyse entropique
        """
        mains = io_json.extraire_mains(partie)
        partie_num = partie.get('partie_number', 'N/A')
        
        # Extraire les séquences INDEX
        sequences = {
            'INDEX1': [main.get('index1') for main in mains],
            'INDEX2': [main.get('index2') for main in mains],
            'INDEX3': [main.get('index3') for main in mains],
            'INDEX5': [main.get('index5') for main in mains]
        }
        
        # Calculer entropies Shannon théoriques et observées
        entropies_shannon_theoriques = {}
        entropies_shannon_observees = {}
        for index_name in ['INDEX1', 'INDEX2', 'INDEX3', 'INDEX5']:
            entropies_shannon_theoriques[index_name] = self.calculer_entropie_shannon_theorique(index_name)
            entropies_shannon_observees[index_name] = self.calculer_entropie_shannon_observee(sequences[index_name])

        # Calculer entropies Rényi théoriques et observées (α=2)
        entropies_renyi_theoriques = {}
        entropies_renyi_observees = {}
        for index_name in ['INDEX1', 'INDEX2', 'INDEX3', 'INDEX5']:
            entropies_renyi_theoriques[index_name] = self.calculer_entropie_renyi_theorique(index_name, alpha=2.0)
            entropies_renyi_observees[index_name] = self.calculer_entropie_renyi_observee(sequences[index_name], alpha=2.0)
        
        # Calculer autocorrélations
        autocorrelations = {}
        for index_name, seq in sequences.items():
            autocorrelations[index_name] = self.calculer_autocorrelation(seq, max_lag=5)
        
        # Détecter anomalies
        anomalies = {}
        for index_name, seq in sequences.items():
            anomalies[index_name] = self.detecter_anomalies_zscore(seq, self.seuil_anomalie)
        
        # Information mutuelle théorique et observée entre INDEX
        info_mutuelle_theorique = {}
        info_mutuelle_observee = {}
        index_pairs = [('INDEX1', 'INDEX2'), ('INDEX1', 'INDEX3'), ('INDEX2', 'INDEX3')]
        for idx1, idx2 in index_pairs:
            key = f"{idx1}_{idx2}"
            info_mutuelle_theorique[key] = self.calculer_information_mutuelle_theorique(idx1, idx2)
            info_mutuelle_observee[key] = self.calculer_information_mutuelle_observee(
                sequences[idx1], sequences[idx2]
            )
        
        return {
            'partie_number': partie_num,
            'nb_mains': len(mains),
            'entropies_shannon_theoriques': entropies_shannon_theoriques,
            'entropies_shannon_observees': entropies_shannon_observees,
            'entropies_renyi_theoriques': entropies_renyi_theoriques,
            'entropies_renyi_observees': entropies_renyi_observees,
            'autocorrelations': autocorrelations,
            'anomalies': anomalies,
            'information_mutuelle_theorique': info_mutuelle_theorique,
            'information_mutuelle_observee': info_mutuelle_observee
        }

    def analyser_toutes_parties_entropique(self) -> Dict[str, Any]:
        """
        Analyse entropique de toutes les parties

        Returns:
            Dict: Résultats d'analyse globale
        """
        print("🔬 DÉMARRAGE ANALYSE ENTROPIQUE GLOBALE...")

        resultats_parties = []
        for i, partie in enumerate(self.parties):
            if i % 100 == 0:
                print(f"📊 Analyse partie {i+1}/{len(self.parties)}")

            resultat_partie = self.analyser_partie_entropique(partie)
            resultats_parties.append(resultat_partie)

        # Calculer statistiques globales
        stats_globales = self._calculer_statistiques_globales(resultats_parties)

        # Détecter singularités inter-parties
        singularites = self._detecter_singularites_inter_parties(resultats_parties)

        return {
            'nb_parties_analysees': len(resultats_parties),
            'resultats_parties': resultats_parties,
            'statistiques_globales': stats_globales,
            'singularites_inter_parties': singularites
        }

    def _calculer_statistiques_globales(self, resultats_parties: List[Dict]) -> Dict[str, Any]:
        """
        Calcule les statistiques globales sur toutes les parties

        Args:
            resultats_parties: Liste des résultats par partie

        Returns:
            Dict: Statistiques globales
        """
        # Collecter toutes les entropies Shannon théoriques et observées
        entropies_shannon_theoriques_globales = {
            'INDEX1': [], 'INDEX2': [], 'INDEX3': [], 'INDEX5': []
        }
        entropies_shannon_observees_globales = {
            'INDEX1': [], 'INDEX2': [], 'INDEX3': [], 'INDEX5': []
        }

        entropies_renyi_theoriques_globales = {
            'INDEX1': [], 'INDEX2': [], 'INDEX3': [], 'INDEX5': []
        }
        entropies_renyi_observees_globales = {
            'INDEX1': [], 'INDEX2': [], 'INDEX3': [], 'INDEX5': []
        }

        info_mutuelle_theorique_globale = {
            'INDEX1_INDEX2': [], 'INDEX1_INDEX3': [], 'INDEX2_INDEX3': []
        }
        info_mutuelle_observee_globale = {
            'INDEX1_INDEX2': [], 'INDEX1_INDEX3': [], 'INDEX2_INDEX3': []
        }

        for resultat in resultats_parties:
            # Entropies Shannon théoriques
            for index_name in entropies_shannon_theoriques_globales.keys():
                val = resultat['entropies_shannon_theoriques'].get(index_name, 0)
                if not np.isnan(val):
                    entropies_shannon_theoriques_globales[index_name].append(val)

            # Entropies Shannon observées
            for index_name in entropies_shannon_observees_globales.keys():
                val = resultat['entropies_shannon_observees'].get(index_name, 0)
                if not np.isnan(val):
                    entropies_shannon_observees_globales[index_name].append(val)

            # Entropies Rényi théoriques
            for index_name in entropies_renyi_theoriques_globales.keys():
                val = resultat['entropies_renyi_theoriques'].get(index_name, 0)
                if not np.isnan(val):
                    entropies_renyi_theoriques_globales[index_name].append(val)

            # Entropies Rényi observées
            for index_name in entropies_renyi_observees_globales.keys():
                val = resultat['entropies_renyi_observees'].get(index_name, 0)
                if not np.isnan(val):
                    entropies_renyi_observees_globales[index_name].append(val)

            # Information mutuelle théorique
            for key in info_mutuelle_theorique_globale.keys():
                val = resultat['information_mutuelle_theorique'].get(key, 0)
                if not np.isnan(val):
                    info_mutuelle_theorique_globale[key].append(val)

            # Information mutuelle observée
            for key in info_mutuelle_observee_globale.keys():
                val = resultat['information_mutuelle_observee'].get(key, 0)
                if not np.isnan(val):
                    info_mutuelle_observee_globale[key].append(val)

        # Calculer statistiques descriptives
        stats = {}

        # Stats entropies Shannon théoriques
        stats['entropies_shannon_theoriques'] = {}
        for index_name, values in entropies_shannon_theoriques_globales.items():
            if values:
                stats['entropies_shannon_theoriques'][index_name] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'median': np.median(values),
                    'q25': np.percentile(values, 25),
                    'q75': np.percentile(values, 75)
                }

        # Stats entropies Shannon observées
        stats['entropies_shannon_observees'] = {}
        for index_name, values in entropies_shannon_observees_globales.items():
            if values:
                stats['entropies_shannon_observees'][index_name] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'median': np.median(values),
                    'q25': np.percentile(values, 25),
                    'q75': np.percentile(values, 75)
                }

        # Stats entropies Rényi théoriques
        stats['entropies_renyi_theoriques'] = {}
        for index_name, values in entropies_renyi_theoriques_globales.items():
            if values:
                stats['entropies_renyi_theoriques'][index_name] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'median': np.median(values)
                }

        # Stats entropies Rényi observées
        stats['entropies_renyi_observees'] = {}
        for index_name, values in entropies_renyi_observees_globales.items():
            if values:
                stats['entropies_renyi_observees'][index_name] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'median': np.median(values)
                }

        # Stats information mutuelle théorique
        stats['information_mutuelle_theorique'] = {}
        for key, values in info_mutuelle_theorique_globale.items():
            if values:
                stats['information_mutuelle_theorique'][key] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'median': np.median(values)
                }

        # Stats information mutuelle observée
        stats['information_mutuelle_observee'] = {}
        for key, values in info_mutuelle_observee_globale.items():
            if values:
                stats['information_mutuelle_observee'][key] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'median': np.median(values)
                }

        return stats

    def _detecter_singularites_inter_parties(self, resultats_parties: List[Dict]) -> Dict[str, Any]:
        """
        Détecte les singularités statistiques entre parties

        Args:
            resultats_parties: Liste des résultats par partie

        Returns:
            Dict: Singularités détectées
        """
        singularites = {
            'entropies_extremes': [],
            'autocorrelations_anormales': [],
            'anomalies_frequentes': []
        }

        # Détecter entropies extrêmes (utiliser les entropies observées)
        for index_name in ['INDEX1', 'INDEX2', 'INDEX3', 'INDEX5']:
            entropies = []
            parties_info = []

            for resultat in resultats_parties:
                entropy_val = resultat['entropies_shannon_observees'].get(index_name, 0)
                if not np.isnan(entropy_val):
                    entropies.append(entropy_val)
                    parties_info.append({
                        'partie_number': resultat['partie_number'],
                        'entropy': entropy_val
                    })

            if len(entropies) > 1:
                mean_entropy = np.mean(entropies)
                std_entropy = np.std(entropies)

                if std_entropy > 0:
                    for info in parties_info:
                        z_score = abs(info['entropy'] - mean_entropy) / std_entropy
                        if z_score > self.seuil_anomalie:
                            singularites['entropies_extremes'].append({
                                'partie_number': info['partie_number'],
                                'index_name': index_name,
                                'entropy': info['entropy'],
                                'z_score': z_score,
                                'type': 'haute' if info['entropy'] > mean_entropy else 'basse'
                            })

        # Détecter autocorrélations anormales
        for index_name in ['INDEX1', 'INDEX2', 'INDEX3', 'INDEX5']:
            for lag in range(1, 6):  # Lags 1-5
                autocorr_values = []
                parties_autocorr = []

                for resultat in resultats_parties:
                    autocorr_dict = resultat['autocorrelations'].get(index_name, {})
                    if lag in autocorr_dict:
                        autocorr_val = autocorr_dict[lag]
                        if not np.isnan(autocorr_val):
                            autocorr_values.append(abs(autocorr_val))  # Valeur absolue
                            parties_autocorr.append({
                                'partie_number': resultat['partie_number'],
                                'autocorr': autocorr_val
                            })

                if len(autocorr_values) > 1:
                    # Détecter autocorrélations significativement élevées
                    threshold = np.percentile(autocorr_values, 95)  # Top 5%

                    for info in parties_autocorr:
                        if abs(info['autocorr']) > threshold and abs(info['autocorr']) > 0.3:
                            singularites['autocorrelations_anormales'].append({
                                'partie_number': info['partie_number'],
                                'index_name': index_name,
                                'lag': lag,
                                'autocorr': info['autocorr'],
                                'threshold': threshold
                            })

        return singularites

    def analyser_calibrage_sensibilite(self) -> Dict[str, Any]:
        """
        Analyse le calibrage de sensibilité du système (60 vs 60M)

        Returns:
            Dict: Analyse du calibrage
        """
        print("⚙️ ANALYSE CALIBRAGE SENSIBILITÉ...")

        # Simuler l'impact d'une main sur les proportions
        base_60 = formules.TOTAL_MAINS_REFERENCE  # 60
        base_60M = 60_000_000  # Base théorique

        # Calculer facteur d'amplification
        facteur_amplification = base_60M / base_60

        # Analyser variation des proportions par main
        variations_par_main = {}

        for index_name, proportions in formules.POINT_ZERO_PROPORTIONS.items():
            variations_par_main[index_name] = {}

            for key, proportion in proportions.items():
                # Impact d'une main sur base 60
                impact_base_60 = (1 / (base_60 + 1)) * 100

                # Impact d'une main sur base 60M
                impact_base_60M = (1 / (base_60M + 1)) * 100

                # Ratio d'amplification
                ratio_amplification = impact_base_60 / impact_base_60M

                variations_par_main[index_name][key] = {
                    'proportion_cible': proportion,
                    'impact_base_60': impact_base_60,
                    'impact_base_60M': impact_base_60M,
                    'ratio_amplification': ratio_amplification
                }

        return {
            'facteur_amplification_global': facteur_amplification,
            'base_reference': base_60,
            'base_theorique': base_60M,
            'variations_par_main': variations_par_main,
            'interpretation': {
                'sensibilite': f"Chaque main a un impact {facteur_amplification:.0f}x plus important",
                'convergence': "Retour vers proportions POINT_ZERO amplifié",
                'stabilite': "Système plus réactif aux déviations locales"
            }
        }

    def generer_rapport_entropique(self, resultats_analyse: Dict[str, Any],
                                 fichier_sortie: str = None) -> str:
        """
        Génère un rapport détaillé d'analyse entropique

        Args:
            resultats_analyse: Résultats de l'analyse entropique
            fichier_sortie: Nom du fichier de sortie

        Returns:
            str: Chemin du fichier généré
        """
        if fichier_sortie is None:
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            fichier_sortie = f"rapport_entropique_{timestamp}.txt"

        with open(fichier_sortie, 'w', encoding='utf-8') as f:
            # En-tête
            f.write("="*80 + "\n")
            f.write("🎓 RAPPORT D'ANALYSE ENTROPIQUE AVANCÉE - EXPERT STATISTICIEN\n")
            f.write("="*80 + "\n\n")

            f.write(f"📊 Nombre de parties analysées : {resultats_analyse['nb_parties_analysees']}\n")
            f.write(f"📁 Fichier source : {self.fichier_json}\n")
            f.write(f"🔬 Seuil anomalie (z-score) : {self.seuil_anomalie}\n")
            f.write("-"*80 + "\n\n")

            # Statistiques globales entropies Shannon observées
            f.write("📈 ENTROPIES DE SHANNON OBSERVÉES - STATISTIQUES GLOBALES\n")
            f.write("-"*80 + "\n")
            stats_shannon_obs = resultats_analyse['statistiques_globales']['entropies_shannon_observees']

            f.write(f"{'INDEX':^12} | {'Moyenne':^10} | {'Écart-type':^12} | {'Min':^8} | {'Max':^8} | {'Médiane':^10}\n")
            f.write("-"*80 + "\n")

            for index_name, stats in stats_shannon_obs.items():
                f.write(f"{index_name:^12} | {stats['mean']:^10.4f} | {stats['std']:^12.4f} | "
                       f"{stats['min']:^8.4f} | {stats['max']:^8.4f} | {stats['median']:^10.4f}\n")

            f.write("\n")

            # Entropies Shannon théoriques
            f.write("📊 ENTROPIES DE SHANNON THÉORIQUES (POINT_ZERO)\n")
            f.write("-"*80 + "\n")
            stats_shannon_theo = resultats_analyse['statistiques_globales']['entropies_shannon_theoriques']

            f.write(f"{'INDEX':^12} | {'Valeur Théorique':^18}\n")
            f.write("-"*40 + "\n")

            for index_name, stats in stats_shannon_theo.items():
                f.write(f"{index_name:^12} | {stats['mean']:^18.4f}\n")  # Théorique = constante

            f.write("\n")

            # Information mutuelle observée
            f.write("🔗 INFORMATION MUTUELLE OBSERVÉE - DÉPENDANCES STATISTIQUES\n")
            f.write("-"*80 + "\n")
            info_mut_obs = resultats_analyse['statistiques_globales']['information_mutuelle_observee']

            f.write(f"{'Paire INDEX':^20} | {'I(X;Y) Moyenne':^15} | {'Écart-type':^12} | {'Interprétation':^20}\n")
            f.write("-"*80 + "\n")

            for pair, stats in info_mut_obs.items():
                interpretation = "Forte dépendance" if stats['mean'] > 0.5 else \
                               "Dépendance modérée" if stats['mean'] > 0.2 else "Faible dépendance"
                f.write(f"{pair:^20} | {stats['mean']:^15.4f} | {stats['std']:^12.4f} | {interpretation:^20}\n")

            f.write("\n")

            # Singularités détectées
            f.write("⚠️  SINGULARITÉS STATISTIQUES DÉTECTÉES\n")
            f.write("-"*80 + "\n")

            singularites = resultats_analyse['singularites_inter_parties']

            # Entropies extrêmes
            f.write(f"🔴 ENTROPIES EXTRÊMES : {len(singularites['entropies_extremes'])} détectées\n")
            for anomalie in singularites['entropies_extremes'][:10]:  # Top 10
                f.write(f"   Partie {anomalie['partie_number']} - {anomalie['index_name']} : "
                       f"H={anomalie['entropy']:.4f} (z-score={anomalie['z_score']:.2f}, {anomalie['type']})\n")

            f.write(f"\n🔄 AUTOCORRÉLATIONS ANORMALES : {len(singularites['autocorrelations_anormales'])} détectées\n")
            for anomalie in singularites['autocorrelations_anormales'][:10]:  # Top 10
                f.write(f"   Partie {anomalie['partie_number']} - {anomalie['index_name']} (lag={anomalie['lag']}) : "
                       f"ρ={anomalie['autocorr']:.4f}\n")

            f.write("\n")

        print(f"📋 Rapport entropique généré : {fichier_sortie}")
        return fichier_sortie

    def executer_analyse_complete(self) -> Dict[str, Any]:
        """
        Exécute l'analyse entropique complète

        Returns:
            Dict: Résultats complets
        """
        print("🎓 DÉMARRAGE ANALYSE ENTROPIQUE COMPLÈTE")
        print("="*60)

        # 1. Analyse entropique de toutes les parties
        resultats_entropique = self.analyser_toutes_parties_entropique()

        # 2. Analyse du calibrage de sensibilité
        analyse_calibrage = self.analyser_calibrage_sensibilite()

        # 3. Combiner les résultats
        resultats_complets = {
            **resultats_entropique,
            'analyse_calibrage': analyse_calibrage
        }

        # 4. Générer le rapport
        fichier_rapport = self.generer_rapport_entropique(resultats_complets)

        print("✅ ANALYSE ENTROPIQUE TERMINÉE")
        print(f"📋 Rapport disponible : {fichier_rapport}")

        return resultats_complets


def main():
    """
    Point d'entrée pour l'analyse entropique
    """
    print("🎓 ANALYSEUR ENTROPIQUE BACCARAT - EXPERT STATISTICIEN")
    print("="*70)

    # Fichier de données
    fichier_json = "dataset_baccarat_lupasco_20250704_092825_condensed.json"

    # Créer l'analyseur
    analyseur = AnalyseurEntropique(fichier_json)

    # Exécuter l'analyse complète
    resultats = analyseur.executer_analyse_complete()

    print("\n🎯 ANALYSE ENTROPIQUE TERMINÉE AVEC SUCCÈS!")


class AnalyseurDecroissanceEntropique:
    """
    🎓 ANALYSEUR SPÉCIALISÉ - DÉCROISSANCE ENTROPIQUE INDEX5
    ========================================================

    Classe experte pour analyser et exploiter la tendance de décroissance
    de l'entropie INDEX5 au cours d'une partie pour améliorer la prédiction.

    Fonctionnalités :
    - Calcul H(INDEX5) pour chaque main n de chaque partie
    - Mesure de la pente de décroissance par partie
    - Corrélation pente vs précision prédictive
    - Identification des seuils optimaux de déclenchement
    """

    def __init__(self, fichier_json: str):
        """
        Initialise l'analyseur de décroissance entropique

        Args:
            fichier_json: Chemin vers le fichier de données
        """
        self.fichier_json = fichier_json
        self.donnees = io_json.charger_donnees_json(fichier_json)
        self.parties = io_json.extraire_parties(self.donnees)
        self.entropie_theorique_index5 = self._calculer_entropie_theorique_index5()

        print("🎓 ANALYSEUR DÉCROISSANCE ENTROPIQUE INDEX5 INITIALISÉ")
        print(f"📊 Parties chargées : {len(self.parties)}")
        print(f"🔬 Entropie théorique INDEX5 : {self.entropie_theorique_index5:.4f} bits")

    def _calculer_entropie_theorique_index5(self) -> float:
        """
        Calcule l'entropie théorique INDEX5 basée sur POINT_ZERO

        Returns:
            float: Entropie théorique INDEX5
        """
        proportions_dict = formules.POINT_ZERO_PROPORTIONS['INDEX5']
        probabilites = [prop/100.0 for prop in proportions_dict.values()]
        return entropy(probabilites, base=2)

    def calculer_entropie_index5_par_main(self, partie: Dict[str, Any]) -> List[Tuple[int, float]]:
        """
        Calcule H(INDEX5) pour chaque main n d'une partie

        Args:
            partie: Données d'une partie

        Returns:
            List[Tuple[int, float]]: Liste (main_n, entropie_INDEX5)
        """
        mains = partie.get('mains', [])
        entropies_par_main = []

        # Pour chaque main n, calculer l'entropie sur les n premières mains
        for n in range(1, len(mains) + 1):
            # Extraire INDEX5 pour les n premières mains
            index5_sequence = []
            for i in range(n):
                main = mains[i]
                index1 = main.get('INDEX1', '')
                index2 = main.get('INDEX2', '')
                index3 = main.get('INDEX3', '')

                if index1 and index2 and index3:
                    index5_value = f"{index1}_{index2}_{index3}"
                    index5_sequence.append(index5_value)

            # Calculer entropie observée pour cette séquence
            if index5_sequence:
                entropie = self._calculer_entropie_shannon_sequence(index5_sequence)
                entropies_par_main.append((n, entropie))

        return entropies_par_main

    def _calculer_entropie_shannon_sequence(self, sequence: List[str]) -> float:
        """
        Calcule l'entropie de Shannon d'une séquence

        Args:
            sequence: Séquence de valeurs

        Returns:
            float: Entropie en bits
        """
        if not sequence:
            return 0.0

        compteur = Counter(sequence)
        total = len(sequence)
        probabilites = [count/total for count in compteur.values()]

        return entropy(probabilites, base=2)

    def mesurer_pente_decroissance(self, entropies_par_main: List[Tuple[int, float]]) -> Dict[str, float]:
        """
        Mesure la pente de décroissance de l'entropie INDEX5

        Args:
            entropies_par_main: Liste (main_n, entropie)

        Returns:
            Dict: Statistiques de la pente
        """
        if len(entropies_par_main) < 3:
            return {'pente': 0.0, 'r_squared': 0.0, 'p_value': 1.0}

        # Extraire x (main_n) et y (entropie)
        x = np.array([point[0] for point in entropies_par_main])
        y = np.array([point[1] for point in entropies_par_main])

        # Régression linéaire
        slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)

        return {
            'pente': slope,
            'intercept': intercept,
            'r_squared': r_value**2,
            'p_value': p_value,
            'std_err': std_err,
            'correlation': r_value
        }

    def analyser_partie_decroissance(self, partie_num: int, partie: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyse complète de la décroissance entropique pour une partie

        Args:
            partie_num: Numéro de la partie
            partie: Données de la partie

        Returns:
            Dict: Analyse complète de la décroissance
        """
        # Calculer entropies par main
        entropies_par_main = self.calculer_entropie_index5_par_main(partie)

        # Mesurer pente de décroissance
        stats_pente = self.mesurer_pente_decroissance(entropies_par_main)

        # Analyser la tendance
        tendance = self._analyser_tendance_decroissance(entropies_par_main, stats_pente)

        # Identifier points de rupture
        points_rupture = self._identifier_points_rupture(entropies_par_main)

        return {
            'partie_number': partie_num,
            'nb_mains': len(entropies_par_main),
            'entropies_par_main': entropies_par_main,
            'statistiques_pente': stats_pente,
            'tendance': tendance,
            'points_rupture': points_rupture,
            'entropie_initiale': entropies_par_main[0][1] if entropies_par_main else 0.0,
            'entropie_finale': entropies_par_main[-1][1] if entropies_par_main else 0.0,
            'decroissance_totale': entropies_par_main[0][1] - entropies_par_main[-1][1] if len(entropies_par_main) >= 2 else 0.0
        }

    def _analyser_tendance_decroissance(self, entropies_par_main: List[Tuple[int, float]],
                                       stats_pente: Dict[str, float]) -> Dict[str, Any]:
        """
        Analyse la tendance de décroissance entropique

        Args:
            entropies_par_main: Liste (main_n, entropie)
            stats_pente: Statistiques de la pente

        Returns:
            Dict: Analyse de la tendance
        """
        pente = stats_pente['pente']
        r_squared = stats_pente['r_squared']
        p_value = stats_pente['p_value']

        # Classification de la tendance
        if p_value > 0.05:
            type_tendance = "NON_SIGNIFICATIVE"
            force = "NULLE"
        elif pente < -0.01 and r_squared > 0.5:
            type_tendance = "DECROISSANCE_FORTE"
            force = "FORTE"
        elif pente < -0.005 and r_squared > 0.3:
            type_tendance = "DECROISSANCE_MODEREE"
            force = "MODEREE"
        elif pente < 0:
            type_tendance = "DECROISSANCE_FAIBLE"
            force = "FAIBLE"
        elif pente > 0.005:
            type_tendance = "CROISSANCE_ANORMALE"
            force = "ANORMALE"
        else:
            type_tendance = "STABLE"
            force = "STABLE"

        # Potentiel prédictif
        if type_tendance in ["DECROISSANCE_FORTE", "DECROISSANCE_MODEREE"]:
            potentiel_predictif = "ELEVE"
        elif type_tendance == "DECROISSANCE_FAIBLE":
            potentiel_predictif = "MODERE"
        else:
            potentiel_predictif = "FAIBLE"

        return {
            'type': type_tendance,
            'force': force,
            'potentiel_predictif': potentiel_predictif,
            'significativite_statistique': p_value < 0.05,
            'qualite_ajustement': r_squared,
            'interpretation': self._interpreter_tendance(type_tendance, pente, r_squared)
        }

    def _interpreter_tendance(self, type_tendance: str, pente: float, r_squared: float) -> str:
        """
        Interprète la tendance pour usage prédictif

        Args:
            type_tendance: Type de tendance détecté
            pente: Valeur de la pente
            r_squared: Qualité de l'ajustement

        Returns:
            str: Interprétation experte
        """
        interpretations = {
            "DECROISSANCE_FORTE": f"Convergence rapide vers patterns dominants (pente={pente:.4f}, R²={r_squared:.3f}). Excellent potentiel prédictif.",
            "DECROISSANCE_MODEREE": f"Convergence progressive identifiée (pente={pente:.4f}, R²={r_squared:.3f}). Bon potentiel prédictif.",
            "DECROISSANCE_FAIBLE": f"Légère tendance à l'ordre (pente={pente:.4f}, R²={r_squared:.3f}). Potentiel prédictif limité.",
            "STABLE": f"Entropie constante (pente={pente:.4f}). Pas d'avantage prédictif.",
            "CROISSANCE_ANORMALE": f"Entropie croissante - anomalie détectée (pente={pente:.4f}). Réviser le modèle.",
            "NON_SIGNIFICATIVE": f"Pas de tendance claire (pente={pente:.4f}, R²={r_squared:.3f}). Bruit statistique."
        }

        return interpretations.get(type_tendance, "Tendance non classifiée.")

    def _identifier_points_rupture(self, entropies_par_main: List[Tuple[int, float]]) -> List[Dict[str, Any]]:
        """
        Identifie les points de rupture dans la décroissance entropique

        Args:
            entropies_par_main: Liste (main_n, entropie)

        Returns:
            List[Dict]: Points de rupture détectés
        """
        if len(entropies_par_main) < 5:
            return []

        points_rupture = []
        entropies = [point[1] for point in entropies_par_main]

        # Détecter changements de pente significatifs
        for i in range(2, len(entropies) - 2):
            # Pente avant
            pente_avant = (entropies[i] - entropies[i-2]) / 2
            # Pente après
            pente_apres = (entropies[i+2] - entropies[i]) / 2

            # Changement de pente significatif
            if abs(pente_avant - pente_apres) > 0.02:
                points_rupture.append({
                    'main_n': entropies_par_main[i][0],
                    'entropie': entropies[i],
                    'pente_avant': pente_avant,
                    'pente_apres': pente_apres,
                    'changement_pente': pente_apres - pente_avant,
                    'type': 'ACCELERATION' if pente_apres < pente_avant else 'DECELERATION'
                })

        return points_rupture

    def analyser_toutes_parties_decroissance(self) -> Dict[str, Any]:
        """
        Analyse la décroissance entropique pour toutes les parties

        Returns:
            Dict: Analyse globale de la décroissance
        """
        print("🔬 ANALYSE DÉCROISSANCE ENTROPIQUE GLOBALE...")

        resultats_parties = []

        for i, partie in enumerate(self.parties):
            if (i + 1) % 100 == 0:
                print(f"📊 Analyse décroissance partie {i+1}/{len(self.parties)}")

            partie_num = i + 1  # Numéro de partie basé sur l'index
            resultat = self.analyser_partie_decroissance(partie_num, partie)
            resultats_parties.append(resultat)

        # Calculer statistiques globales
        stats_globales = self._calculer_statistiques_globales_decroissance(resultats_parties)

        # Identifier seuils optimaux
        seuils_optimaux = self._identifier_seuils_optimaux(resultats_parties)

        # Corréler avec précision prédictive
        correlation_predictive = self._analyser_correlation_predictive(resultats_parties)

        return {
            'nb_parties_analysees': len(resultats_parties),
            'resultats_parties': resultats_parties,
            'statistiques_globales': stats_globales,
            'seuils_optimaux': seuils_optimaux,
            'correlation_predictive': correlation_predictive
        }

    def _calculer_statistiques_globales_decroissance(self, resultats_parties: List[Dict]) -> Dict[str, Any]:
        """
        Calcule les statistiques globales de décroissance

        Args:
            resultats_parties: Résultats de toutes les parties

        Returns:
            Dict: Statistiques globales
        """
        # Collecter toutes les pentes
        pentes = [r['statistiques_pente']['pente'] for r in resultats_parties
                 if r['statistiques_pente']['p_value'] < 0.05]

        # Collecter R²
        r_squared_values = [r['statistiques_pente']['r_squared'] for r in resultats_parties]

        # Collecter décroissances totales
        decroissances_totales = [r['decroissance_totale'] for r in resultats_parties]

        # Types de tendances
        types_tendances = [r['tendance']['type'] for r in resultats_parties]
        compteur_tendances = Counter(types_tendances)

        # Potentiels prédictifs
        potentiels_predictifs = [r['tendance']['potentiel_predictif'] for r in resultats_parties]
        compteur_potentiels = Counter(potentiels_predictifs)

        return {
            'pentes': {
                'moyenne': np.mean(pentes) if pentes else 0.0,
                'std': np.std(pentes) if pentes else 0.0,
                'min': np.min(pentes) if pentes else 0.0,
                'max': np.max(pentes) if pentes else 0.0,
                'median': np.median(pentes) if pentes else 0.0,
                'nb_significatives': len(pentes)
            },
            'qualite_ajustement': {
                'r_squared_moyen': np.mean(r_squared_values),
                'r_squared_std': np.std(r_squared_values),
                'nb_bon_ajustement': sum(1 for r2 in r_squared_values if r2 > 0.5)
            },
            'decroissances_totales': {
                'moyenne': np.mean(decroissances_totales),
                'std': np.std(decroissances_totales),
                'min': np.min(decroissances_totales),
                'max': np.max(decroissances_totales)
            },
            'distribution_tendances': dict(compteur_tendances),
            'distribution_potentiels': dict(compteur_potentiels),
            'pourcentage_decroissance_significative': (len(pentes) / len(resultats_parties)) * 100
        }

    def _identifier_seuils_optimaux(self, resultats_parties: List[Dict]) -> Dict[str, float]:
        """
        Identifie les seuils optimaux pour déclenchement prédictif

        Args:
            resultats_parties: Résultats de toutes les parties

        Returns:
            Dict: Seuils optimaux identifiés
        """
        # Parties avec décroissance forte/modérée
        parties_exploitables = [r for r in resultats_parties
                              if r['tendance']['potentiel_predictif'] in ['ELEVE', 'MODERE']]

        if not parties_exploitables:
            return {
                'seuil_pente': -0.005,
                'seuil_r_squared': 0.3,
                'seuil_main_declenchement': 20,
                'nb_parties_exploitables': 0,
                'pourcentage_exploitable': 0.0
            }

        # Seuils basés sur percentiles
        pentes_exploitables = [r['statistiques_pente']['pente'] for r in parties_exploitables]
        r_squared_exploitables = [r['statistiques_pente']['r_squared'] for r in parties_exploitables]

        seuil_pente = np.percentile(pentes_exploitables, 75)  # 75e percentile
        seuil_r_squared = np.percentile(r_squared_exploitables, 25)  # 25e percentile

        # Analyser à partir de quelle main la décroissance devient détectable
        mains_detection = []
        for partie in parties_exploitables:
            entropies = partie['entropies_par_main']
            for i in range(10, len(entropies)):  # Minimum 10 mains
                # Calculer pente sur les i premières mains
                x = np.array([e[0] for e in entropies[:i]])
                y = np.array([e[1] for e in entropies[:i]])
                if len(x) >= 3:
                    slope, _, r_value, p_value, _ = stats.linregress(x, y)
                    if slope < seuil_pente and r_value**2 > seuil_r_squared and p_value < 0.05:
                        mains_detection.append(i)
                        break

        seuil_main_declenchement = np.median(mains_detection) if mains_detection else 20

        return {
            'seuil_pente': seuil_pente,
            'seuil_r_squared': seuil_r_squared,
            'seuil_main_declenchement': int(seuil_main_declenchement),
            'nb_parties_exploitables': len(parties_exploitables),
            'pourcentage_exploitable': (len(parties_exploitables) / len(resultats_parties)) * 100
        }

    def _analyser_correlation_predictive(self, resultats_parties: List[Dict]) -> Dict[str, Any]:
        """
        Analyse la corrélation entre décroissance et potentiel prédictif

        Args:
            resultats_parties: Résultats de toutes les parties

        Returns:
            Dict: Analyse de corrélation prédictive
        """
        # Créer scores prédictifs basés sur la tendance
        scores_predictifs = []
        pentes = []
        r_squared_values = []

        for partie in resultats_parties:
            # Score prédictif basé sur le potentiel
            potentiel = partie['tendance']['potentiel_predictif']
            if potentiel == 'ELEVE':
                score = 3
            elif potentiel == 'MODERE':
                score = 2
            elif potentiel == 'FAIBLE':
                score = 1
            else:
                score = 0

            scores_predictifs.append(score)
            pentes.append(partie['statistiques_pente']['pente'])
            r_squared_values.append(partie['statistiques_pente']['r_squared'])

        # Corrélations
        corr_pente_score = np.corrcoef(pentes, scores_predictifs)[0, 1] if len(pentes) > 1 else 0
        corr_r2_score = np.corrcoef(r_squared_values, scores_predictifs)[0, 1] if len(r_squared_values) > 1 else 0

        return {
            'correlation_pente_potentiel': corr_pente_score,
            'correlation_r2_potentiel': corr_r2_score,
            'distribution_scores': Counter(scores_predictifs),
            'interpretation': {
                'pente': "Forte corrélation négative" if corr_pente_score < -0.5 else
                        "Corrélation modérée" if corr_pente_score < -0.3 else "Faible corrélation",
                'r_squared': "Forte corrélation positive" if corr_r2_score > 0.5 else
                           "Corrélation modérée" if corr_r2_score > 0.3 else "Faible corrélation"
            }
        }

    def generer_rapport_decroissance(self, resultats_analyse: Dict[str, Any],
                                   fichier_sortie: str = None) -> str:
        """
        Génère un rapport détaillé d'analyse de décroissance

        Args:
            resultats_analyse: Résultats de l'analyse de décroissance
            fichier_sortie: Nom du fichier de sortie

        Returns:
            str: Chemin du fichier généré
        """
        if fichier_sortie is None:
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            fichier_sortie = f"rapport_decroissance_index5_{timestamp}.txt"

        with open(fichier_sortie, 'w', encoding='utf-8') as f:
            # En-tête
            f.write("="*80 + "\n")
            f.write("🎓 RAPPORT DÉCROISSANCE ENTROPIQUE INDEX5 - EXPERT STATISTICIEN\n")
            f.write("="*80 + "\n\n")

            f.write(f"📊 Nombre de parties analysées : {resultats_analyse['nb_parties_analysees']}\n")
            f.write(f"📁 Fichier source : {self.fichier_json}\n")
            f.write(f"🔬 Entropie théorique INDEX5 : {self.entropie_theorique_index5:.4f} bits\n")
            f.write("-"*80 + "\n\n")

            # Statistiques globales
            stats = resultats_analyse['statistiques_globales']

            f.write("📈 STATISTIQUES GLOBALES DÉCROISSANCE\n")
            f.write("-"*80 + "\n")
            f.write(f"Pourcentage parties avec décroissance significative : {stats['pourcentage_decroissance_significative']:.1f}%\n")
            f.write(f"Pente moyenne : {stats['pentes']['moyenne']:.6f} ± {stats['pentes']['std']:.6f}\n")
            f.write(f"R² moyen : {stats['qualite_ajustement']['r_squared_moyen']:.4f}\n")
            f.write(f"Décroissance totale moyenne : {stats['decroissances_totales']['moyenne']:.4f} bits\n\n")

            # Distribution des tendances
            f.write("📊 DISTRIBUTION DES TENDANCES\n")
            f.write("-"*80 + "\n")
            for tendance, count in stats['distribution_tendances'].items():
                pourcentage = (count / resultats_analyse['nb_parties_analysees']) * 100
                f.write(f"{tendance:25} : {count:4d} parties ({pourcentage:5.1f}%)\n")
            f.write("\n")

            # Seuils optimaux
            seuils = resultats_analyse['seuils_optimaux']
            f.write("🎯 SEUILS OPTIMAUX IDENTIFIÉS\n")
            f.write("-"*80 + "\n")
            f.write(f"Seuil pente déclenchement : {seuils['seuil_pente']:.6f}\n")
            f.write(f"Seuil R² minimum : {seuils['seuil_r_squared']:.4f}\n")
            f.write(f"Main optimale déclenchement : {seuils['seuil_main_declenchement']}\n")
            f.write(f"Parties exploitables : {seuils['nb_parties_exploitables']} ({seuils['pourcentage_exploitable']:.1f}%)\n\n")

            # Corrélation prédictive
            corr = resultats_analyse['correlation_predictive']
            f.write("🔗 CORRÉLATION AVEC POTENTIEL PRÉDICTIF\n")
            f.write("-"*80 + "\n")
            f.write(f"Corrélation pente-potentiel : {corr['correlation_pente_potentiel']:.4f} ({corr['interpretation']['pente']})\n")
            f.write(f"Corrélation R²-potentiel : {corr['correlation_r2_potentiel']:.4f} ({corr['interpretation']['r_squared']})\n\n")

        print(f"📋 Rapport décroissance généré : {fichier_sortie}")
        return fichier_sortie

    def executer_analyse_decroissance_complete(self) -> Dict[str, Any]:
        """
        Exécute l'analyse complète de décroissance entropique INDEX5

        Returns:
            Dict: Résultats complets
        """
        print("🎓 DÉMARRAGE ANALYSE DÉCROISSANCE ENTROPIQUE INDEX5")
        print("="*60)

        # Analyse de toutes les parties
        resultats = self.analyser_toutes_parties_decroissance()

        # Générer le rapport
        fichier_rapport = self.generer_rapport_decroissance(resultats)

        print("✅ ANALYSE DÉCROISSANCE TERMINÉE")
        print(f"📋 Rapport disponible : {fichier_rapport}")

        return resultats


def main_decroissance():
    """
    Point d'entrée pour l'analyse de décroissance entropique INDEX5
    """
    print("🎓 ANALYSEUR DÉCROISSANCE ENTROPIQUE INDEX5 - EXPERT STATISTICIEN")
    print("="*70)

    # Fichier de données
    fichier_json = "dataset_baccarat_lupasco_20250704_092825_condensed.json"

    # Créer l'analyseur de décroissance
    analyseur_decroissance = AnalyseurDecroissanceEntropique(fichier_json)

    # Exécuter l'analyse complète
    resultats = analyseur_decroissance.executer_analyse_decroissance_complete()

    print("\n🎯 ANALYSE DÉCROISSANCE INDEX5 TERMINÉE AVEC SUCCÈS!")
    print(f"📊 {resultats['statistiques_globales']['pourcentage_decroissance_significative']:.1f}% des parties montrent une décroissance significative")
    print(f"🎯 {resultats['seuils_optimaux']['pourcentage_exploitable']:.1f}% des parties sont exploitables pour la prédiction")


if __name__ == "__main__":
    # Choix du mode d'analyse
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "decroissance":
        main_decroissance()
    else:
        main()
